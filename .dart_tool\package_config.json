{"configVersion": 2, "packages": [{"name": "charcode", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/charcode-1.1.3", "packageUri": "lib/", "languageVersion": "2.0"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/collection-1.14.13", "packageUri": "lib/", "languageVersion": "2.3"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http-0.12.2", "packageUri": "lib/", "languageVersion": "2.4"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_parser-3.1.4", "packageUri": "lib/", "languageVersion": "2.3"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/meta-1.2.4", "packageUri": "lib/", "languageVersion": "1.12"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path-1.7.0", "packageUri": "lib/", "languageVersion": "2.0"}, {"name": "pedantic", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pedantic-1.9.2", "packageUri": "lib/", "languageVersion": "2.1"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_span-1.7.0", "packageUri": "lib/", "languageVersion": "2.6"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/string_scanner-1.0.5", "packageUri": "lib/", "languageVersion": "2.0"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/term_glyph-1.1.0", "packageUri": "lib/", "languageVersion": "1.8"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/typed_data-1.2.0", "packageUri": "lib/", "languageVersion": "2.4"}, {"name": "dart", "rootUri": "../", "packageUri": "lib/", "languageVersion": "2.7"}], "generated": "2021-01-14T13:47:18.000033Z", "generator": "pub", "generatorVersion": "2.12.0-133.2.beta"}