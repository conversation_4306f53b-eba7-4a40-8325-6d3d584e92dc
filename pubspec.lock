# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.3"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.14.13"
  http:
    dependency: "direct main"
    description:
      name: http
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.4"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.4"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.7.0"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.9.2"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.7.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.5"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
sdks:
  dart: ">=2.7.0 <3.0.0"
